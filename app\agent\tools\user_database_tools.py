"""
User database tools for retrieving user information from external PostgreSQL database.

This module contains tools for querying user data from an external PostgreSQL database
containing user profiles with first_name, last_name, preferred_name, title, and bio.
"""

import logging
import asyncio
import json
from typing import Dict, Any, List, Optional
import asyncpg
from contextlib import asynccontextmanager

from app.agent.tools.base import BaseTool, ToolSchema, ToolResult, tool_registry
from app.core.config import settings

logger = logging.getLogger(__name__)


class UserDatabaseTool(BaseTool):
    """Tool for retrieving user information from external PostgreSQL database."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self._connection_pool = None
        self.user_table_name = self.config.get("user_table_name", "user")
        self.user_style_table_name = self.config.get("user_style_table_name", "user_style")
        self.user_style_mapping_table_name = self.config.get("user_style_mapping_table_name", "user_style_mapping")
        self.client_table_name = self.config.get("client_table_name", "client")
        self.user_assignment_table_name = self.config.get("user_assignment_table_name", "user_assignment")

    async def get_pool(self):
        """Get or create a database connection pool."""
        if self._connection_pool is None:
            logger.info(f"[DEBUG] Creating new database connection pool")
            if not settings.external_user_db_url:
                logger.error(f"[DEBUG] External user database URL not configured")
                raise ValueError("External user database URL not configured. Please set EXTERNAL_USER_DB_URL environment variable.")
            try:
                logger.info(f"[DEBUG] Attempting to create pool with URL: {settings.external_user_db_url[:50]}...")
                self._connection_pool = await asyncpg.create_pool(settings.external_user_db_url)
                logger.info(f"[DEBUG] Database connection pool created successfully")
            except Exception as e:
                logger.error(f"[DEBUG] Failed to create database connection pool: {str(e)}")
                logger.error(f"[DEBUG] Pool creation traceback:", exc_info=True)
                raise
        return self._connection_pool

    @asynccontextmanager
    async def get_connection(self):
        """Get a database connection from the pool."""
        logger.debug(f"[DEBUG] Acquiring database connection from pool")
        pool = await self.get_pool()
        connection = None
        try:
            connection = await pool.acquire()
            logger.debug(f"[DEBUG] Database connection acquired successfully")
            yield connection
        except Exception as e:
            logger.error(f"[DEBUG] Error with database connection: {str(e)}")
            logger.error(f"[DEBUG] Connection error traceback:", exc_info=True)
            raise
        finally:
            if connection:
                logger.debug(f"[DEBUG] Releasing database connection back to pool")
                await pool.release(connection)

    @property
    def name(self) -> str:
        return "user_database_lookup"

    @property
    def description(self) -> str:
        return "Retrieve user information from external PostgreSQL database including name, title, and bio"

    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "search_value": {
                        "type": "string",
                        "description": "Value to search for (name, email, title, or 'me' for current user)."
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of results to return",
                        "default": 1,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": ["search_value"]
            }
        )

    async def execute(self, user_context: Optional[Dict[str, Any]] = None, **kwargs) -> ToolResult:
        """
        Retrieve user information from external database.
        
        Args:
            user_context: User context containing user_id, tenant_id, client_id, session_key
            search_value: Value to search for (use "me" to search for current user)
            limit: Maximum number of results
            
        Returns:
            ToolResult with user information
        """
        logger.info(f"[DEBUG] UserDatabaseTool.execute started with user_context: {user_context}, kwargs: {kwargs}")
        try:
            search_value = kwargs.get("search_value", "").strip()
            limit = kwargs.get("limit", 1)

            if not search_value:
                return ToolResult(
                    success=False,
                    error="search_value is required"
                )

            # Handle "me" search by looking up current user ID

            users = []
            query_params = []

            async with self.get_connection() as conn:
                if search_value.lower() == "me":
                    if not user_context or not user_context.get("user_id") or not user_context.get("email"):
                        return ToolResult(
                            success=False,
                            error="Cannot search for 'me' without valid user context"
                        )
                    
                    user_id = user_context.get("user_id")
                    user_email = user_context.get("email", "").lower()
                
                    # Base query without tenant filtering
                    query = f"""
                        SELECT u.id, u.first_name, u.last_name, u.preferred_name, u.title, u.email, u.bio, u.manager_id, u.settings,
                               COALESCE(u.preferred_name, CONCAT(u.first_name, ' ', u.last_name)) as display_name
                        FROM "{self.user_table_name}" u
                        WHERE u.id = $1 OR LOWER(u.email) = $2
                    """
                    query_params = [user_id, user_email]
                else:
                    # Use a tsquery for full-text search with proper escaping
                    # We'll search for the value as a whole phrase and also with terms OR'd together
                    # Escape special characters and use parameterized queries for safety
                    search_terms = ' & '.join(search_value.split())
                    # Check if we need to filter by client
                    client_id = user_context.get("client_id") if user_context else None
                    
                    if not client_id:
                        # Query without client filtering
                        query = f"""
                            SELECT u.id, u.first_name, u.last_name, u.preferred_name, u.title, u.email, u.bio, u.manager_id, u.settings,
                                   ts_rank(to_tsvector('english', COALESCE(u.first_name, '') || ' ' || COALESCE(u.last_name, '') || ' ' || COALESCE(u.preferred_name, '') || ' ' || COALESCE(u.title, '') || ' ' || COALESCE(u.email, '')), to_tsquery('english', $2)) as rank,
                                   COALESCE(u.preferred_name, CONCAT(u.first_name, ' ', u.last_name)) as display_name
                            FROM "{self.user_table_name}" u
                            WHERE to_tsvector('english', COALESCE(u.first_name, '') || ' ' || COALESCE(u.last_name, '') || ' ' || COALESCE(u.preferred_name, '') || ' ' || COALESCE(u.title, '') || ' ' || COALESCE(u.email, '')) @@ to_tsquery('english', $2)
                            ORDER BY rank DESC
                            LIMIT $1
                        """
                        query_params = [limit, search_terms]
                    else:
                        # Query with client filtering - filter by users assigned to the client
                        query = f"""
                            SELECT u.id, u.first_name, u.last_name, u.preferred_name, u.title, u.email, u.bio, u.manager_id, u.settings,
                                   ts_rank(to_tsvector('english', COALESCE(u.first_name, '') || ' ' || COALESCE(u.last_name, '') || ' ' || COALESCE(u.preferred_name, '') || ' ' || COALESCE(u.title, '') || ' ' || COALESCE(u.email, '')), to_tsquery('english', $2)) as rank,
                                   COALESCE(u.preferred_name, CONCAT(u.first_name, ' ', u.last_name)) as display_name
                            FROM "{self.user_table_name}" u
                            WHERE to_tsvector('english', COALESCE(u.first_name, '') || ' ' || COALESCE(u.last_name, '') || ' ' || COALESCE(u.preferred_name, '') || ' ' || COALESCE(u.title, '') || ' ' || COALESCE(u.email, '')) @@ to_tsquery('english', $2)
                            AND EXISTS (SELECT 1 FROM {self.user_assignment_table_name} ua WHERE ua.user_id = u.id AND ua.client_id = $3)
                            ORDER BY rank DESC
                            LIMIT $1
                        """
                        query_params = [limit, search_terms, client_id]

                rows = await conn.fetch(query, *query_params)
                users = [dict(row) for row in rows]

                if users:
                    for user in users:
                        # Fetch manager information if manager_id is present
                        if user.get('manager_id'):
                            manager_query = f'''
                                SELECT id, first_name, last_name, email
                                FROM "{self.user_table_name}"
                                WHERE id = $1
                            '''
                            manager_row = await conn.fetchrow(manager_query, user['manager_id'])
                            if manager_row:
                                user['manager'] = dict(manager_row)

                        user_settings = user.get('settings')
                        user_settings_data = json.loads(user_settings) if isinstance(user_settings, str) else user_settings or {}
                        xip_code_settings = user_settings_data.get('xip_code', {})

                        enabled_styles = []
                        if xip_code_settings.get('show_communication_style'):
                            enabled_styles.append('Communication Style')
                        if xip_code_settings.get('show_work_style'):
                            enabled_styles.append('Work Style')
                        if xip_code_settings.get('show_conflict_management_style'):
                            enabled_styles.append('Conflict Management Style')

                        if not enabled_styles:
                            continue
                        
                        # Subquery to fetch and filter styles for each user based on their settings
                        style_query = f'''
                            SELECT usm.style_name, us.style_value
                            FROM {self.user_style_table_name} us
                            JOIN {self.user_style_mapping_table_name} usm ON us.style_id = usm.id
                            WHERE us.user_id = $1
                              AND usm.style_name = ANY($2::text[])
                              AND us.is_deleted = false AND us.status = 'active'
                              AND usm.is_deleted = false AND usm.status = 'active'
                        '''
                        style_rows = await conn.fetch(style_query, user['id'], enabled_styles)

                        if style_rows:
                            user['styles'] = []
                            for style_row in style_rows:
                                user['styles'].append({style_row['style_name']: style_row['style_value']})

                return ToolResult(
                    success=True,
                    result={
                        "search_value": search_value,
                        "users": users,
                        "total_found": len(users),
                        "limit_applied": limit
                    },
                    metadata={
                        "operation": "user_database_lookup",
                        "result_count": len(users),
                        "search_type": "user_id" if search_value.lower() == "me" else "full_text"
                    }
                )

        except ValueError as e:
            logger.error(f"[DEBUG] User database lookup validation error: {str(e)}")
            return ToolResult(success=False, error=str(e))
        except Exception as e:
            logger.error(f"[DEBUG] User database lookup error: {str(e)}")
            return ToolResult(success=False, error=f"Database lookup failed: {str(e)}")


# Register tools
tool_registry.register_tool(UserDatabaseTool())
